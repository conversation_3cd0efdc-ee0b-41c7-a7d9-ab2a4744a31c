{"name": "orderit", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "preview": "vite preview", "lint": "eslint . --ext .ts,.tsx", "lint:fix": "eslint . --ext .ts,.tsx --fix"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@tailwindcss/vite": "^4.1.7", "ag-grid-react": "^33.3.1", "axios": "^1.9.0", "lucide-react": "^0.511.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hook-form": "^7.56.4", "react-hot-toast": "^2.5.2", "react-modern-drawer": "^1.4.0", "react-router-dom": "^7.6.1", "swiper": "^11.2.8", "tailwindcss": "^4.1.7"}, "devDependencies": {"@eslint/js": "^9.27.0", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@types/react-slick": "^0.23.13", "@types/swiper": "^5.4.3", "@vitejs/plugin-react": "^4.4.1", "eslint": "^9.27.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-prettier": "^5.4.0", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "eslint-plugin-unused-imports": "^4.1.4", "globals": "^16.2.0", "typescript": "~5.8.3", "typescript-eslint": "^8.32.1", "vite": "^6.3.5"}}
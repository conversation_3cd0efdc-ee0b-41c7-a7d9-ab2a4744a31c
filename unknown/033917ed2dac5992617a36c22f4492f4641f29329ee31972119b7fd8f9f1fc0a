import { DndContext } from "@dnd-kit/core";
import DraggableCard from "./DraggableCard";
import DroppableBox from "./DroppableBox";
import SortableList from "./SortableList";

const Demo = () => {
  const handleDragEnd = ({ active, over }) => {
    console.log("Dropped", active.id, "over", over?.id);
  };
  return (
    <DndContext onDragEnd={handleDragEnd}>
      Demo
      <DraggableCard id="card-1">Drag me around 👋</DraggableCard>
      <DroppableBox id="drop-zone-1" />
      <SortableList/>
    </DndContext>
  );
};

export default Demo;

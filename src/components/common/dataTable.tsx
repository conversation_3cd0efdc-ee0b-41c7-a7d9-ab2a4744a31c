import { AgGridReact } from "ag-grid-react";
import React from "react";
import type { ColDef, ICellRendererParams } from "ag-grid-community";
// Define the API response type for menu items
interface MenuItemAPI {
  id: number;
  name: string;
  description: string;
  price: string | number;
  category: {
    id: string;
    name: string;
    slug?: string;
  };
  image?: string;
  is_active?: boolean;
  created_at?: string;
  updated_at?: string;
}
import CustomButton from "./CustomButton";
import Icon from "./Icon";

// Create type-safe cell renderer params
interface PhotoCellRendererParams extends ICellRendererParams {
  value: string;
}

interface TextCellRendererParams extends ICellRendererParams {
  value: string;
}

interface PriceCellRendererParams extends ICellRendererParams {
  value: number;
}

interface DataTableProps {
  menuItems: MenuItemAPI[];
}

const DataTable: React.FC<DataTableProps> = ({ menuItems }) => {
  const colDefs: ColDef<MenuItemAPI>[] = [
    {
      field: "image",
      headerName: "Photo",
      cellRenderer: (params: PhotoCellRendererParams) => {
        return (
          <div className="flex justify-center max-w-[80px]">
            <img
              src={params.value || "/food.png"}
              alt="Product"
              className="rounded-md object-cover"
            />
          </div>
        );
      },
      width: 100,
    },
    {
      field: "name",
      headerName: "Product Name",
      cellRenderer: (params: TextCellRendererParams) => {
        return <span className="font-semibold">{params.value}</span>;
      },
      width: 200,
    },
    {
      field: "price",
      headerName: "Price",
      cellRenderer: (params: PriceCellRendererParams) => {
        const price =
          typeof params.value === "string"
            ? parseFloat(params.value)
            : params.value;
        return (
          <span className="text-orange-600 font-medium">
            ₹{price.toFixed(2)}
          </span>
        );
      },
      width: 100,
    },
    {
      field: "category",
      headerName: "Category",
      cellRenderer: (params: any) => {
        const categoryName = params.value?.name || "Unknown";
        return (
          <div
            className={`flex items-center py-[2px] px-[10px] text-[var(--color-green)] border border-[var(--color-green)] rounded-2xl`}
          >
            <span
              className={`w-2 h-2 rounded-full mr-2 bg-[var(--color-green)]`}
            ></span>
            <span className="text-sm">{categoryName}</span>
          </div>
        );
      },
      width: 120,
    },
    {
      field: "description",
      headerName: "Description",
      cellRenderer: (params: TextCellRendererParams) => {
        return (
          <span className="text-sm text-gray-600 line-clamp-2">
            {params.value || "No description"}
          </span>
        );
      },
      width: 200,
    },
    {
      field: "is_active",
      headerName: "Status",
      cellRenderer: (params: any) => {
        const isActive = params.value !== false;
        return (
          <div className="flex justify-center">
            {isActive ? (
              <CustomButton
                label="Active"
                icon={<Icon name="Eye" height={16} width={16} />}
                bgColor="bg-[var(--color-green-light)]"
                className="border border-active !px-3 !py-[6px]"
                textColor="text-[var(--color-active)]"
              />
            ) : (
              <CustomButton
                label="Inactive"
                icon={<Icon name="EyeOff" height={16} width={16} />}
                bgColor="bg-red-light"
                className="border border-red !px-3 !py-[6px]"
                textColor="text-red"
              />
            )}
          </div>
        );
      },
      width: 120,
    },
    {
      headerName: "Action",
      cellRenderer: () => {
        return (
          <div className="flex justify-center">
            <button className="p-1">
              <Icon name="Ellipsis" />
            </button>
          </div>
        );
      },
      width: 100,
    },
  ];

  return (
    <div className="w-full h-screen">
      <div className="custom-ag-table w-full h-full">
        <AgGridReact<MenuItemAPI>
          rowData={menuItems}
          columnDefs={colDefs}
          getRowClass={() => "custom-row"}
          defaultColDef={{
            resizable: true,
            sortable: true,
            flex: 1,
          }}
          domLayout="normal"
          rowHeight={120}
          headerHeight={50}
          className="rounded-md"
        />
      </div>
    </div>
  );
};

export default DataTable;

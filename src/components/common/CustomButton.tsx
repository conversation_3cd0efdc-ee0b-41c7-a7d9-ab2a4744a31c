import React from "react";

interface CustomButtonProps {
  label: string;
  onClick?: () => void;
  icon?: React.ReactNode;
  bgColor?: string;
  textColor?: string;
  className?: string;
  disabled?: boolean;
}

const CustomButton: React.FC<CustomButtonProps> = ({
  label,
  onClick,
  icon,
  bgColor = "bg-[var(--color-orange)]",
  textColor = "text-white",
  className = "",
  disabled = false,
}) => {
  return (
    <button
      onClick={onClick}
      disabled={disabled}
      className={`flex gap-1 items-center cursor-pointer justify-center px-2 text-xs py-1 md:px-3 md:py-1.5 md:text-sm rounded-md transition-all duration-300 ${bgColor} ${textColor} ${
        disabled ? "opacity-50 cursor-not-allowed" : "hover:opacity-90"
      } ${className}`}
    >
      {icon && <span>{icon}</span>}
      <span>{label}</span>
    </button>
  );
};

export default CustomButton;

import React, { useState, memo } from "react";
// Define the API response type for menu items
interface MenuItemAPI {
  id: number;
  name: string;
  description: string;
  price: string | number;
  category: {
    id: string;
    name: string;
    slug?: string;
  };
  image?: string;
  is_active?: boolean;
  created_at?: string;
  updated_at?: string;
}
import CustomButton from "./CustomButton";
import Icon from "./Icon";

interface MobileMenuProps {
  menuItems: MenuItemAPI[];
}

const MobileMenu: React.FC<MobileMenuProps> = ({ menuItems }) => {
  const [activeButton, setActiveButton] = useState<number | null>(null);

  const toggleButton = (buttonIndex: number) => {
    if (activeButton === buttonIndex) {
      setActiveButton(null);
    } else {
      setActiveButton(buttonIndex);
    }
  };

  return (
    <ul className="flex flex-col space-y-2">
      {/* {menuItems?.map((menuItem, index) => {
        const price =
          typeof menuItem.price === "string"
            ? parseFloat(menuItem.price)
            : menuItem.price;
        const isActive = menuItem.is_active !== false;

        return (
          <li
            key={index}
            className={`w-full text-left p-[10px] rounded-md ${
              activeButton === menuItem.id
                ? "bg-orange-light border-orange-border border"
                : "bg-white border border-[#E6E6E6]"
            }`}
          >
            <div onClick={() => toggleButton(menuItem.id)}>
              <div className="flex items-start">
                <div>
                  <Icon name="EllipsisVertical" height={20} width={20} />
                </div>
                <div className="flex items-center max-w-11">
                  <img
                    src={menuItem.image || "/food.png"}
                    alt={menuItem.name}
                    className="rounded-md object-cover"
                  />
                </div>
                <div className="flex flex-col mx-[10px] text-left">
                  <span className="font-semibold text-sm lg:text-base">
                    {menuItem.name}
                  </span>
                  <span className="text-orange font-semibold text-sm lg:text-base">
                    ₹{price.toFixed(2)}
                  </span>
                </div>
                <div className="flex justify-between">
                  {isActive ? (
                    <CustomButton
                      label="Active"
                      icon={<Icon name="Eye" height={16} width={16} />}
                      bgColor="bg-[var(--color-green-light)]"
                      className="border border-active !px-3 !py-[6px]"
                      textColor="text-[var(--color-active)]"
                    />
                  ) : (
                    <CustomButton
                      label="Inactive"
                      icon={<Icon name="EyeOff" height={16} width={16} />}
                      bgColor="bg-red-light"
                      className="border border-red !px-3 !py-[6px]"
                      textColor="text-red"
                    />
                  )}
                </div>
              </div>
            </div>

            {activeButton === menuItem.id && (
              <div className="text-sm mt-4">
                <ul>
                  <li className="flex mb-2">
                    <span className="font-medium basis-[30%]">Category:</span>
                    <span>{menuItem.category?.name || "Unknown"}</span>
                  </li>
                  <li className="flex mb-2">
                    <span className="font-medium basis-[30%]">
                      Description:
                    </span>
                    <span>{menuItem.description || "No description"}</span>
                  </li>
                </ul>
              </div>
            )}
          </li>
        );
      })} */}
    </ul>
  );
};

export default memo(MobileMenu);

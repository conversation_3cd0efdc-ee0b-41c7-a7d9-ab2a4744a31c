import React from "react";

interface IconButtonProps {
  icon: React.ReactNode;
  variant?: "primary" | "light";
  onClick?: () => void;
  className?: string;
}

const IconButton: React.FC<IconButtonProps> = ({
  icon,
  variant = "light",
  onClick,
  className = "",
}) => {
  const bgClass =
    variant === "primary"
      ? "bg-[var(--color-orange)] text-white"
      : "bg-[var(--color-orange-light)]";

  return (
    <button
      className={`md:w-9 md:h-9 rounded-sm w-7 h-7 md:rounded-xl flex items-center justify-center cursor-pointer ${bgClass} ${className}`}
      onClick={onClick}
    >
      {icon}
    </button>
  );
};

export default IconButton;

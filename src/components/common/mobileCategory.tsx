import React, { useState, memo } from "react";
import Icon from "./Icon";

// Define the API response type for categories
interface CategoryAPI {
  id: string;
  name: string;
  description: string;
  outlet: string;
  slug?: string;
  is_active?: boolean;
  created_at?: string;
  updated_at?: string;
}

interface MobileCategoryProps {
  categories: CategoryAPI[];
}

const MobileCategory: React.FC<MobileCategoryProps> = ({ categories }) => {
  const [activeButton, setActiveButton] = useState<string | null>(null);

  const toggleButton = (buttonIndex: string) => {
    if (activeButton === buttonIndex) {
      setActiveButton(null);
    } else {
      setActiveButton(buttonIndex);
    }
  };

  return (
    <ul className="flex flex-col space-y-2">
      {categories?.map((category) => {
        return (
          <li
            key={category.id}
            className={`w-full text-left p-[10px] rounded-md ${
              activeButton === category.id
                ? "bg-orange-light border-orange-border border"
                : "bg-white border border-[#E6E6E6]"
            }`}
          >
            <div onClick={() => toggleButton(category.id)}>
              <div className="flex items-start">
                <div>
                  <Icon name="EllipsisVertical" height={20} width={20} />
                </div>
                <div className="flex flex-col mx-[10px] text-left flex-1">
                  <span className="font-semibold text-sm lg:text-base mb-2">
                    {category.name}
                  </span>
                  <span className="text-gray-600 text-sm">
                    {category.description || "No description"}
                  </span>
                </div>
              </div>
            </div>

            {activeButton === category.id && (
              <div className="text-sm mt-4">
                <ul>
                  <li className="flex mb-2">
                    <span className="font-medium basis-[30%]">Slug:</span>
                    <span>{category.slug || "N/A"}</span>
                  </li>
                  <li className="flex mb-2">
                    <span className="font-medium basis-[30%]">Created:</span>
                    <span>
                      {category.created_at
                        ? new Date(category.created_at).toLocaleDateString()
                        : "N/A"}
                    </span>
                  </li>
                </ul>
              </div>
            )}
          </li>
        );
      })}
    </ul>
  );
};

export default memo(MobileCategory);

import React, { useEffect, useState } from "react";
import axios from "axios";
import withAuthProtection from "../../utils/withAuthProtection";
import Icon from "../common/Icon";
import { getApiUrl } from "../../utils/api";
import InputField from "../common/InputField";
import CustomButton from "../common/CustomButton";
import CategoryDataTable from "../common/categoryDataTable";
import MobileCategory from "../common/mobileCategory";
import toast from "react-hot-toast";


// Interface for category data from API
interface Category {
  id: string;
  name: string;
  description: string;
  outlet: string;
  slug?: string;
  is_active?: boolean;
  created_at?: string;
  updated_at?: string;
}

const CategoriesListPage: React.FC = () => {
  const [categories, setCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);


  const getCategories = async () => {

    try {
      setLoading(true);
      setError(null);

      const res = await axios.get(getApiUrl("/restaurant/categories/"), {
        withCredentials: true,
        headers: {
          "Content-Type": "application/json",
        },
      });

      setCategories(res.data);
    } catch (error) {
      console.error("Error fetching categories:", error);
      setError("Failed to load categories");
      toast.error("Failed to load categories");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    getCategories();
  }, []);

  // Show loading state
  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-orange mx-auto mb-4"></div>
          <p className="text-gray-600">Loading categories...</p>
        </div>
      </div>
    );
  }

  // Show error state
  if (error) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <p className="text-red-600 mb-4">{error}</p>
          <button
            onClick={getCategories}
            className="bg-orange text-white px-4 py-2 rounded-md hover:bg-orange/90"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div>
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-800 mb-2">Categories</h1>
        <p className="text-gray-600">Manage your restaurant categories</p>
      </div>

      <div className="mb-2 flex items-center gap-3">
        <div className="grow">
          <InputField
            id="search"
            name="search"
            type="text"
            placeholder="Search categories..."
            className="bg-white border border-[#E6E6E6] rounded-lg"
            icon={<Icon name="Search" width={20} height={20} />}
            containerClassName="max-w-[700px]"
          />
        </div>
        <div className="flex items-center gap-4">
          <CustomButton
            label="Add Category"
            icon={<Icon name="Plus" width={16} height={16} />}
            bgColor="bg-[var(--color-orange)]"
            textColor="text-white"
            className="px-4 py-2"
            onClick={() => (window.location.href = "/categories/add")}
          />
        </div>
      </div>

      <div className="md:hidden">
        <MobileCategory categories={categories} />
      </div>

      <div className="hidden md:block">
        <CategoryDataTable categories={categories} />
      </div>
    </div>
  );
};

// export default withAuthProtection(CategoriesListPage);
export default CategoriesListPage;

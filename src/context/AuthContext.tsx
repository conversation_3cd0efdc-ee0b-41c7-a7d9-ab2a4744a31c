import React, { createContext, useState, type ReactNode } from "react";

// Define the context type
interface AuthContextType {
  userInfo: string | null;
  setUserInfo: (userInfo: string | null) => void;
  loading: boolean;
  setLoading: (loading: boolean) => void;
}

export const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider: React.FC<{ children: ReactNode }> = ({
  children,
}) => {
  const [userInfo, setUserInfo] = useState<string | null>(null);
  const [loading, setLoading] = useState<boolean>(false);
  console.log("AuthProvider initialized");

  return (
    <AuthContext.Provider value={{ userInfo, setUserInfo, loading, setLoading }}>
      {children}
    </AuthContext.Provider>
  );
};

// Custom hook to use the AuthContext
export const useAuth = () => {
  const context = React.useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

import React from "react";

// Higher-order component - now just returns the component without auth check
const withAuthProtection = <P extends object>(
  WrappedComponent: React.ComponentType<P>,
) => {
  const WithAuthProtection: React.FC<P> = (props) => {
    // No auth check needed since tokens are in cookies
    return <WrappedComponent {...props} />;
  };

  return WithAuthProtection;
};

export default withAuthProtection;

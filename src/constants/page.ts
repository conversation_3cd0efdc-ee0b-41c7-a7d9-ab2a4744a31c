import type { Page } from "../types/common";

export const PAGE_DASHBOARD: Page = {
  id: "DASHBOARD",
  path: "/dashboard",
  name: "Dashboard",
};

export const PAGE_CATEGORIES: Page = {
  id: "CATEGORIES",
  path: "/categories",
  name: "CATEGORIES",
};

export const PAGE_CATEGORIES_LIST: Page = {
  id: "CATEGORIES_LIST",
  path: "/categories/list",
  name: "CATEGORIES_LIST",
};

export const PAGE_CATEGORIES_ADD: Page = {
  id: "CATEGORIES_ADD",
  path: "/categories/add",
  name: "CATEGORIES_ADD",
};

export const PAGE_MENU_LIST: Page = {
  id: "MENU_LIST",
  path: "/",
  name: "MENU_LIST",
};

export const PAGE_MENU_ADD: Page = {
  id: "MENU_ADD",
  path: "/menu/add",
  name: "MENU_ADD",
};

export const PAGE_ORDER: Page = {
  id: "ORDER",
  path: "/order",
  name: "ORDE<PERSON>",
};

export const PAGE_MENUS: Page = {
  id: "MENUS",
  path: "/menus",
  name: "MENUS",
};

export const PAGE_LOGIN: Page = {
  id: "LOGIN",
  path: "/login",
  name: "LOGIN",
};
